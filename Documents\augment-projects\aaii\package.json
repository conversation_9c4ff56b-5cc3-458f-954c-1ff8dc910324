{"name": "greyhound-multi-bet-analyzer", "version": "1.0.0", "description": "Node.js application for identifying high-probability greyhound racing multi-bet opportunities", "main": "multiFinder.js", "type": "module", "scripts": {"start": "node multiFinder.js", "dev": "node --watch multiFinder.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["greyhound", "racing", "betting", "multi-bet", "scraping", "analysis"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "winston": "^3.11.0", "node-cron": "^3.0.3", "fs-extra": "^11.2.0", "chalk": "^5.3.0"}, "engines": {"node": ">=16.0.0"}}